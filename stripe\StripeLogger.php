<?php

/**
 * Stripe Logger Class
 *
 * A comprehensive logging system for Stripe-related operations with multiple log levels,
 * console output, file logging, and email notifications.
 */
class StripeLogLevel
{
    // Log levels from lowest to highest severity
    public const DEBUG = 'debug';         // Detailed debug information
    public const INFO = 'info';           // Interesting events
    public const NOTICE = 'notice';       // Normal but significant events
    public const WARNING = 'warning';     // Exceptional occurrences that are not errors
    public const ERROR = 'error';         // Runtime errors that don't require immediate action
    public const CRITICAL = 'critical';   // Critical conditions (e.g., component unavailable)
    public const ALERT = 'alert';         // Action must be taken immediately
    public const EMERGENCY = 'emergency'; // System is unusable
    // Get all available log levels
    public static function all(): array
    {
        return [
            self::DEBUG,
            self::INFO,
            self::NOTICE,
            self::WARNING,
            self::ERROR,
            self::CRITICAL,
            self::ALERT,
            self::EMERGENCY
        ];
    }
    // Get log levels that should be considered errors
    public static function errorLevels(): array
    {
        return [self::ERROR, self::CRITICAL, self::ALERT, self::EMERGENCY];
    }
}
class StripeLogger
{
    // Ana yapılandırma - tüm loglama sistemini etkinleştir/devre dışı bırak
    private static bool $isEnabled = true;
    // Admin e-posta adresi
    private static string $adminEmail = '<EMAIL>';
    // Konsol çıktısı yapılandırması - hangi log seviyeleri konsola yazılacak
    private static array $consoleOutputLevels = [
        StripeLogLevel::DEBUG     => false,
        StripeLogLevel::INFO      => false,
        StripeLogLevel::NOTICE    => false,
        StripeLogLevel::WARNING   => true,
        StripeLogLevel::ERROR     => true,
        StripeLogLevel::CRITICAL  => true,
        StripeLogLevel::ALERT     => true,
        StripeLogLevel::EMERGENCY => true
    ];
    // E-posta bildirimi yapılandırması - hangi log seviyeleri için e-posta gönderilecek
    private static array $emailNotificationLevels = [
        StripeLogLevel::DEBUG     => false,
        StripeLogLevel::INFO      => false,
        StripeLogLevel::NOTICE    => false,
        StripeLogLevel::WARNING   => true,
        StripeLogLevel::ERROR     => true,
        StripeLogLevel::CRITICAL  => true,
        StripeLogLevel::ALERT     => true,
        StripeLogLevel::EMERGENCY => true
    ];
    // Console-friendly ASCII symbols for each log level
    private static array $levelSymbols = [
        StripeLogLevel::DEBUG     => '[DEBUG]',
        StripeLogLevel::INFO      => '[INFO]',
        StripeLogLevel::NOTICE    => '[NOTICE]',
        StripeLogLevel::WARNING   => '[WARNING]',
        StripeLogLevel::ERROR     => '[ERROR]',
        StripeLogLevel::CRITICAL  => '[CRITICAL]',
        StripeLogLevel::ALERT     => '[ALERT]',
        StripeLogLevel::EMERGENCY => '[EMERGENCY]',
    ];
    // Console-friendly ASCII symbols for Stripe events
    private static array $stripeEventSymbols = [
        // Payment events
        'payment_intent.succeeded'      => '[PAYMENT-SUCCESS]',
        'payment_intent.payment_failed' => '[PAYMENT-FAILED]',
        'charge.succeeded'              => '[CHARGE-SUCCESS]',
        // Invoice events
        'invoice.paid'                  => '[INVOICE-PAID]',
        'invoice.payment_failed'        => '[INVOICE-FAILED]',
        'invoice.updated'               => '[INVOICE-UPDATED]',
        'invoice.payment_succeeded'     => '[INVOICE-SUCCESS]',
        'invoiceitem.created'           => '[INVOICE-ITEM-CREATED]',
        // Subscription events
        'customer.subscription.created'  => '[SUB-CREATED]',
        'customer.subscription.updated'  => '[SUB-UPDATED]',
        'customer.subscription.deleted'  => '[SUB-DELETED]',
        // Customer events
        'customer.created'              => '[CUSTOMER-CREATED]',
        'customer.updated'              => '[CUSTOMER-UPDATED]',
        // Product events
        'product.created'               => '[PRODUCT-CREATED]',
        'product.updated'               => '[PRODUCT-UPDATED]',
        'product.deleted'               => '[PRODUCT-DELETED]',
        // Price events
        'price.created'                 => '[PRICE-CREATED]',
        'price.updated'                 => '[PRICE-UPDATED]',
        'price.deleted'                 => '[PRICE-DELETED]',
        // Checkout events
        'checkout.session.completed'    => '[CHECKOUT-COMPLETED]',
        // Default symbol for other events
        'default'                       => '[STRIPE-EVENT]',
    ];
    public static function init(): void
    {
        date_default_timezone_set('Europe/Istanbul');
    }
    /**
     * Log a message with the specified level
     */
    public static function log(string $level, string $message, array $context = []): void
    {
        if (!self::$isEnabled || !in_array($level, StripeLogLevel::all())) {
            return;
        }
        self::init();
        $timestamp = date('Y-m-d H:i:s');
        $symbol = self::$levelSymbols[$level] ?? "[$level]";
        // Format context data if provided
        $contextStr = '';
        if (!empty($context)) {
            $contextStr = ' | Context: ' . json_encode($context, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        }
        // Create log entry
        $logLine = "{$symbol} [{$timestamp} UTC+3] {$message}{$contextStr}";
        // Write to log file using standard error_log
        error_log($logLine);
        // Output to console if enabled for this level
        if (self::$consoleOutputLevels[$level] ?? false) {
            error_log($logLine);
        }
        // Send email notification if enabled for this level
        if (self::$emailNotificationLevels[$level] ?? false) {
            self::sendEmailNotification($level, $logLine);
        }
    }
    /**
     * Log a Stripe webhook event
     */
    public static function logStripeEvent(string $eventType, string $eventId, string $message, array $context = []): void
    {
        self::init();
        $level = self::determineLogLevelForEvent($eventType);
        $symbol = self::$stripeEventSymbols[$eventType] ?? self::$stripeEventSymbols['default'];
        $timestamp = date('Y-m-d H:i:s');
        // Format context data if provided
        $contextStr = '';
        if (!empty($context)) {
            $contextStr = ' | Data: ' . json_encode($context, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        }
        // Create log entry
        $logLine = "{$symbol} STRIPE EVENT [{$timestamp} UTC+3] Type: {$eventType}, ID: {$eventId} | {$message}{$contextStr}";
        // Write to log file using standard error_log
        error_log($logLine);
        // Output to console if enabled for this level
        if (self::$consoleOutputLevels[$level] ?? false) {
            error_log($logLine);
        }
        // Send email notification if enabled for this level
        if (self::$emailNotificationLevels[$level] ?? false) {
            self::sendEmailNotification($level, $logLine);
        }
    }
    /**
     * Determine appropriate log level based on event type
     */
    private static function determineLogLevelForEvent(string $eventType): string
    {
        // Payment failures are errors
        if (strpos($eventType, '.failed') !== false || strpos($eventType, '.error') !== false) {
            return StripeLogLevel::ERROR;
        }
        // Deletions are warnings
        if (strpos($eventType, '.deleted') !== false) {
            return StripeLogLevel::WARNING;
        }
        // Updates are notices
        if (strpos($eventType, '.updated') !== false) {
            return StripeLogLevel::NOTICE;
        }
        // Creations and successes are info
        return StripeLogLevel::INFO;
    }
    /**
     * Send email notification
     */
    private static function sendEmailNotification(string $level, string $logLine): void
    {
        require_once __DIR__ . '/../mailsender.php';
        $to = [self::$adminEmail];
        $subject = "Stripe Log [{$level}] - " . date('Y-m-d H:i:s');
        // Create HTML email body
        $messageBody = "
<div style='display: flex; flex-direction: column; width: auto; border-width: 1px; border-style: solid; border-color: #334155; border-radius: 0.5rem; padding: 16px; background-color: #0f172a; color: #f8fafc; font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, Oxygen, Ubuntu, Cantarell, \"Open Sans\", \"Helvetica Neue\", sans-serif;'>
    <div style='padding-bottom: 12px; border-bottom: 1px solid #1e293b; margin-bottom: 12px;'>
        <p style='font-size: 20px; font-weight: 600; color: #f8fafc; margin: 0;'>Stripe Log Alert</p>
        <p style='font-size: 14px; font-weight: 500; color: #94a3b8; margin: 4px 0 0 0;'>Level: {$level}</p>
    </div>
    <div style='margin-top: 16px; padding: 12px; background-color: #1e293b; border-radius: 0.375rem;'>
        <p style='font-size: 14px; font-weight: 400; color: #f8fafc; margin: 0; line-height: 1.5; white-space: pre-wrap;'>{$logLine}</p>
    </div>
    <div style='margin-top: 20px; padding-top: 16px; border-top: 1px solid #1e293b;'>
        <p style='font-size: 12px; color: #94a3b8; margin: 0;'>Sent via <span style='color: #0ea5e9; font-weight: 500;'>CoinScout Stripe Logger</span> at " . date('Y-m-d H:i:s') . "</p>
    </div>
</div>";
        // Call the send_mail function from mailsender.php
        send_mail($messageBody, $to, $subject);
    }
}
